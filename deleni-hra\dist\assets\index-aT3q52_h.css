@import"https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap";:root{font-family:Comic Sans MS,cursive,system-ui,Avenir,Helvetica,Arial,sans-serif;line-height:1.5;font-weight:400;color-scheme:light;color:#333;background:linear-gradient(135deg,#667eea,#764ba2);font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-text-size-adjust:100%}body{margin:0;min-width:320px;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);background-attachment:fixed}#root{width:100%;min-height:100vh}input{border:none;outline:none;font-family:inherit}input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}input[type=number]{-moz-appearance:textfield}button:focus,input:focus{outline:3px solid #ff6b6b;outline-offset:2px}*{transition:all .2s ease}::-webkit-scrollbar{width:8px}::-webkit-scrollbar-track{background:#ffffff1a}::-webkit-scrollbar-thumb{background:#ffffff4d;border-radius:4px}::-webkit-scrollbar-thumb:hover{background:#ffffff80}*{margin:0;padding:0;box-sizing:border-box}body{font-family:Roboto,Noto Sans,Droid Sans,Helvetica Neue,Arial,sans-serif;background:linear-gradient(135deg,#667eea,#764ba2);min-height:100vh;overflow-x:hidden}.app{min-height:100vh;display:flex;align-items:center;justify-content:center;padding:20px}.welcome-screen{text-align:center;background:#fffffff2;padding:60px 40px;border-radius:30px;box-shadow:0 20px 40px #0003;max-width:500px;width:100%;animation:bounceIn 1s ease-out}.title{font-size:3rem;color:#ff6b6b;margin-bottom:20px;text-shadow:2px 2px 4px rgba(0,0,0,.1);animation:pulse 2s infinite}.subtitle{font-size:1.3rem;color:#4ecdc4;margin-bottom:40px;font-weight:600}.start-button{background:linear-gradient(45deg,#ff6b6b,#ffa726);color:#fff;border:none;padding:20px 40px;font-size:1.5rem;border-radius:50px;cursor:pointer;font-weight:700;box-shadow:0 10px 20px #ff6b6b4d;transition:all .3s ease;font-family:inherit}.start-button:hover{transform:translateY(-3px);box-shadow:0 15px 30px #ff6b6b66}.game-screen{background:#fffffff2;padding:40px;border-radius:30px;box-shadow:0 20px 40px #0003;max-width:800px;width:100%;animation:slideIn .5s ease-out}.header{margin-bottom:40px}.score-board{display:flex;justify-content:space-around;gap:20px}.score,.attempts{background:linear-gradient(45deg,#4ecdc4,#44a08d);color:#fff;padding:15px 25px;border-radius:25px;font-size:1.2rem;font-weight:700;box-shadow:0 5px 15px #4ecdc44d}.problem-container{margin:40px 0;text-align:center}.problem{display:flex;align-items:center;justify-content:center;gap:20px;flex-wrap:wrap;font-size:2.5rem;font-weight:700;color:#333}.dividend,.divisor{background:linear-gradient(45deg,#ff9a9e,#fecfef);padding:20px 30px;border-radius:20px;color:#333;box-shadow:0 8px 16px #ff9a9e4d;min-width:80px;text-align:center}.operator,.equals,.remainder-text{color:#666;font-size:2rem}.number-selector{display:flex;flex-direction:column;align-items:center;gap:5px;transition:all .3s ease;position:relative}.number-selector.active{transform:scale(1.05)}.input-group{display:flex;flex-direction:column;align-items:center;gap:5px}.input-label{font-size:1rem;font-weight:700;color:#333;text-align:center}.number-input{background:linear-gradient(45deg,#a8edea,#fed6e3);border:3px solid #4ecdc4;border-radius:15px;padding:0;font-size:2rem;font-weight:700;text-align:center;width:100px;height:90px;color:#333;font-family:inherit;transition:all .3s ease;outline:none;-webkit-appearance:none;-moz-appearance:textfield;box-sizing:border-box}.number-input::-webkit-outer-spin-button,.number-input::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}.number-input:focus{transform:translateY(-2px);box-shadow:0 5px 15px #4ecdc44d;border-color:#ff6b6b}.number-selector.active .number-input{border-color:#ff6b6b;box-shadow:0 0 20px #ff6b6b4d;animation:pulse 2s infinite}.number-input:disabled{opacity:.6;cursor:not-allowed}.number-buttons{display:grid;grid-template-columns:repeat(5,1fr);gap:10px;max-width:350px}.number-btn{background:linear-gradient(45deg,#667eea,#764ba2);color:#fff;border:none;border-radius:12px;padding:12px;font-size:1.2rem;font-weight:700;cursor:pointer;transition:all .2s ease;font-family:inherit;min-width:50px;min-height:50px}.number-btn:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 5px 15px #667eea66}.number-btn.selected{background:linear-gradient(45deg,#ff6b6b,#ffa726);transform:scale(1.1);box-shadow:0 5px 15px #ff6b6b66}.number-btn:disabled{opacity:.5;cursor:not-allowed;transform:none}.answer-input{background:linear-gradient(45deg,#a8edea,#fed6e3);border:3px solid #4ecdc4;border-radius:15px;padding:15px 20px;font-size:2rem;font-weight:700;text-align:center;width:100px;color:#333;font-family:inherit;transition:all .3s ease}.answer-input:focus{outline:none;border-color:#ff6b6b;transform:scale(1.05);box-shadow:0 0 20px #ff6b6b4d}.answer-input:disabled{opacity:.7;cursor:not-allowed}.buttons{display:flex;justify-content:center;gap:20px;margin:40px 0;flex-wrap:wrap}.check-button,.next-button{padding:15px 30px;font-size:1.3rem;border:none;border-radius:25px;cursor:pointer;font-weight:700;font-family:inherit;transition:all .3s ease;min-width:180px}.check-button{background:linear-gradient(45deg,#56ab2f,#a8e6cf);color:#fff;box-shadow:0 8px 16px #56ab2f4d}.check-button:hover:not(:disabled){transform:translateY(-3px);box-shadow:0 12px 24px #56ab2f66}.check-button:disabled{background:#ccc;cursor:not-allowed;transform:none;box-shadow:none}.next-button{background:linear-gradient(45deg,#667eea,#764ba2);color:#fff;box-shadow:0 8px 16px #667eea4d}.next-button:hover{transform:translateY(-3px);box-shadow:0 12px 24px #667eea66}.feedback{text-align:center;padding:20px;border-radius:20px;font-size:1.5rem;font-weight:700;margin:20px 0;animation:fadeIn .5s ease-in}.feedback.correct{background:linear-gradient(45deg,#56ab2f,#a8e6cf);color:#fff;box-shadow:0 8px 16px #56ab2f4d}.feedback.incorrect{background:linear-gradient(45deg,#ff6b6b,#ffa726);color:#fff;box-shadow:0 8px 16px #ff6b6b4d}@keyframes bounceIn{0%{opacity:0;transform:scale(.3)}50%{opacity:1;transform:scale(1.05)}70%{transform:scale(.9)}to{opacity:1;transform:scale(1)}}@keyframes slideIn{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}@keyframes slideUp{0%{opacity:0;transform:translateY(50px) scale(.9)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 768px){.app{padding:10px}.welcome-screen{padding:40px 20px;margin:10px}.title{font-size:2.2rem}.subtitle{font-size:1.1rem}.game-screen{padding:20px;margin:10px}.problem{font-size:2rem;gap:15px}.dividend,.divisor{padding:15px 20px;min-width:60px;font-size:2rem}.answer-input{width:80px;padding:12px 15px;font-size:1.8rem}.selected-number{width:80px;padding:12px 15px;font-size:1.8rem;min-height:50px}.number-buttons{max-width:250px;gap:6px}.number-btn{min-width:40px;min-height:40px;padding:8px;font-size:1rem}.keyboard-popup{padding:25px;width:95vw;max-width:320px}.input-label{font-size:.9rem}.number-keypad{max-width:240px;gap:10px}.keypad-btn{width:60px;height:60px;font-size:1.5rem}.score-board{flex-direction:column;gap:10px}.score,.attempts{padding:12px 20px;font-size:1rem}.buttons{flex-direction:column;gap:15px}.check-button,.next-button{min-width:auto;width:100%;padding:12px 20px;font-size:1.1rem}}@media (max-width: 480px){.problem{font-size:1.5rem;gap:10px}.dividend,.divisor{padding:10px 15px;font-size:1.5rem;min-width:50px}.answer-input{width:60px;padding:8px 10px;font-size:1.5rem}.number-input{width:60px;padding:0;font-size:1.5rem;height:60px}.input-label{font-size:.8rem}.operator,.equals,.remainder-text{font-size:1.3rem}}@media (hover: none) and (pointer: coarse){.start-button,.check-button,.next-button{padding:18px 35px;font-size:1.4rem}.answer-input{padding:18px 20px;font-size:2.2rem}.number-input{padding:0;font-size:2.2rem;height:90px}.number-btn{min-width:55px;min-height:55px;padding:15px;font-size:1.3rem}.number-buttons{gap:10px}.keyboard-popup{padding:35px;width:90vw;max-width:380px}.number-keypad{max-width:320px;gap:15px}.keypad-btn{width:80px;height:80px;font-size:2rem}}
