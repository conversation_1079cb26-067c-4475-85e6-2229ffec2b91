/* Reset a základní styly */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Comic Sans MS', cursive, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Uví<PERSON><PERSON><PERSON> o<PERSON> */
.welcome-screen {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 60px 40px;
  border-radius: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
  animation: bounceIn 1s ease-out;
}

.title {
  font-size: 3rem;
  color: #ff6b6b;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

.subtitle {
  font-size: 1.3rem;
  color: #4ecdc4;
  margin-bottom: 40px;
  font-weight: 600;
}

.start-button {
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  color: white;
  border: none;
  padding: 20px 40px;
  font-size: 1.5rem;
  border-radius: 50px;
  cursor: pointer;
  font-weight: bold;
  box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
  font-family: inherit;
}

.start-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(255, 107, 107, 0.4);
}

/* Herní obrazovka */
.game-screen {
  background: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 800px;
  width: 100%;
  animation: slideIn 0.5s ease-out;
}

.header {
  margin-bottom: 40px;
}

.score-board {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.score, .attempts {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  padding: 15px 25px;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: bold;
  box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3);
}

/* Příklad */
.problem-container {
  margin: 40px 0;
  text-align: center;
}

.problem {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
}

.dividend, .divisor {
  background: linear-gradient(45deg, #ff9a9e, #fecfef);
  padding: 20px 30px;
  border-radius: 20px;
  color: #333;
  box-shadow: 0 8px 16px rgba(255, 154, 158, 0.3);
  min-width: 80px;
  text-align: center;
}

.operator, .equals, .remainder-text {
  color: #666;
  font-size: 2rem;
}

/* Number Selector komponenta */
.number-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.selected-number {
  background: linear-gradient(45deg, #a8edea, #fed6e3);
  border: 3px solid #4ecdc4;
  border-radius: 15px;
  padding: 15px 20px;
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  width: 100px;
  color: #333;
  font-family: inherit;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.number-buttons {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  max-width: 300px;
}

.number-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  min-width: 50px;
  min-height: 50px;
}

.number-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.number-btn.selected {
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.number-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Zachování starých stylů pro kompatibilitu */
.answer-input {
  background: linear-gradient(45deg, #a8edea, #fed6e3);
  border: 3px solid #4ecdc4;
  border-radius: 15px;
  padding: 15px 20px;
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  width: 100px;
  color: #333;
  font-family: inherit;
  transition: all 0.3s ease;
}

.answer-input:focus {
  outline: none;
  border-color: #ff6b6b;
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
}

.answer-input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Tlačítka */
.buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 40px 0;
  flex-wrap: wrap;
}

.check-button, .next-button {
  padding: 15px 30px;
  font-size: 1.3rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: bold;
  font-family: inherit;
  transition: all 0.3s ease;
  min-width: 180px;
}

.check-button {
  background: linear-gradient(45deg, #56ab2f, #a8e6cf);
  color: white;
  box-shadow: 0 8px 16px rgba(86, 171, 47, 0.3);
}

.check-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 24px rgba(86, 171, 47, 0.4);
}

.check-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.next-button {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.next-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
}

/* Zpětná vazba */
.feedback {
  text-align: center;
  padding: 20px;
  border-radius: 20px;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 20px 0;
  animation: fadeIn 0.5s ease-in;
}

.feedback.correct {
  background: linear-gradient(45deg, #56ab2f, #a8e6cf);
  color: white;
  box-shadow: 0 8px 16px rgba(86, 171, 47, 0.3);
}

.feedback.incorrect {
  background: linear-gradient(45deg, #ff6b6b, #ffa726);
  color: white;
  box-shadow: 0 8px 16px rgba(255, 107, 107, 0.3);
}

/* Animace */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Responzivní design pro mobilní zařízení */
@media (max-width: 768px) {
  .app {
    padding: 10px;
  }

  .welcome-screen {
    padding: 40px 20px;
    margin: 10px;
  }

  .title {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .game-screen {
    padding: 20px;
    margin: 10px;
  }

  .problem {
    font-size: 2rem;
    gap: 15px;
  }

  .dividend, .divisor {
    padding: 15px 20px;
    min-width: 60px;
    font-size: 2rem;
  }

  .answer-input {
    width: 80px;
    padding: 12px 15px;
    font-size: 1.8rem;
  }

  .selected-number {
    width: 80px;
    padding: 12px 15px;
    font-size: 1.8rem;
    min-height: 50px;
  }

  .number-buttons {
    max-width: 250px;
    gap: 6px;
  }

  .number-btn {
    min-width: 40px;
    min-height: 40px;
    padding: 8px;
    font-size: 1rem;
  }

  .score-board {
    flex-direction: column;
    gap: 10px;
  }

  .score, .attempts {
    padding: 12px 20px;
    font-size: 1rem;
  }

  .buttons {
    flex-direction: column;
    gap: 15px;
  }

  .check-button, .next-button {
    min-width: auto;
    width: 100%;
    padding: 12px 20px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .problem {
    font-size: 1.5rem;
    gap: 10px;
  }

  .dividend, .divisor {
    padding: 10px 15px;
    font-size: 1.5rem;
    min-width: 50px;
  }

  .answer-input {
    width: 60px;
    padding: 8px 10px;
    font-size: 1.5rem;
  }

  .selected-number {
    width: 60px;
    padding: 8px 10px;
    font-size: 1.5rem;
    min-height: 40px;
  }

  .number-buttons {
    max-width: 200px;
    gap: 4px;
  }

  .number-btn {
    min-width: 35px;
    min-height: 35px;
    padding: 6px;
    font-size: 0.9rem;
  }

  .operator, .equals, .remainder-text {
    font-size: 1.3rem;
  }
}

/* Dotykové optimalizace */
@media (hover: none) and (pointer: coarse) {
  .start-button, .check-button, .next-button {
    padding: 18px 35px;
    font-size: 1.4rem;
  }

  .answer-input {
    padding: 18px 20px;
    font-size: 2.2rem;
  }

  .selected-number {
    padding: 18px 20px;
    font-size: 2.2rem;
    min-height: 70px;
  }

  .number-btn {
    min-width: 55px;
    min-height: 55px;
    padding: 15px;
    font-size: 1.3rem;
  }

  .number-buttons {
    gap: 10px;
  }
}
