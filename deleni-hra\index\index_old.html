<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Výukové programy pro děti</title>
    <link rel="icon" href="favicon.jpg">
    <style>
        /* Základní styly */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Comic Sans MS', 'Chalkboard SE', 'Arial', sans-serif;
        }

        body {
            background-color: #f0f9ff;
            background-image: linear-gradient(to bottom, #f0f9ff, #e1f5fe);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Hlavička */
        header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #4caf50;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #2196f3;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            color: #4caf50;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        /* Karty programů */
        .programs-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 40px;
        }

        .program-card {
            background-color: white;
            border-radius: 20px;
            overflow: hidden;
            width: 300px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            text-decoration: none;
            color: inherit;
            position: relative;
        }

        .program-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            height: 120px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 1.8rem;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="10" cy="10" r="5" fill="rgba(255,255,255,0.2)"/><circle cx="30" cy="30" r="8" fill="rgba(255,255,255,0.2)"/><circle cx="50" cy="10" r="6" fill="rgba(255,255,255,0.2)"/><circle cx="70" cy="40" r="10" fill="rgba(255,255,255,0.2)"/><circle cx="90" cy="20" r="7" fill="rgba(255,255,255,0.2)"/></svg>');
            opacity: 0.5;
        }

        .pexeso .card-header {
            background-color: #ff9800;
        }

        .podstatna-jmena .card-header {
            background-color: #9c27b0;
        }

        .geometrie .card-header {
            background-color: #2196f3;
        }

	.hamster .card-header {
            background-color: #ff4400;
        }

        .pizza .card-header {
            background-color: #ff6b35;
        }

        .card-body {
            padding: 20px;
            text-align: center;
        }

        .card-description {
            color: #555;
            margin-bottom: 20px;
            font-size: 1rem;
            line-height: 1.5;
        }

        .card-button {
            display: inline-block;
            background-color: #4caf50;
            color: white;
            padding: 10px 20px;
            border-radius: 30px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .pexeso .card-button:hover {
            background-color: #ff9800;
        }

        .podstatna-jmena .card-button:hover {
            background-color: #9c27b0;
        }

        .geometrie .card-button:hover {
            background-color: #2196f3;
        }

        .hamster .card-button:hover {
            background-color: #ff4400;
        }

        .pizza .card-button:hover {
            background-color: #ff6b35;
        }

        /* Ikony pro karty */
        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        /* Animace */
        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        .floating {
            animation: float 4s ease-in-out infinite;
        }

        /* Dekorace */
        .decoration {
            position: absolute;
            z-index: -1;
            opacity: 0.6;
        }

        .decoration-1 {
            top: 10%;
            left: 5%;
            font-size: 3rem;
            color: #ff9800;
            transform: rotate(-15deg);
        }

        .decoration-2 {
            top: 20%;
            right: 5%;
            font-size: 2.5rem;
            color: #9c27b0;
            transform: rotate(20deg);
        }

        .decoration-3 {
            bottom: 10%;
            left: 10%;
            font-size: 2.8rem;
            color: #2196f3;
            transform: rotate(10deg);
        }

        .decoration-4 {
            bottom: 15%;
            right: 10%;
            font-size: 3.2rem;
            color: #4caf50;
            transform: rotate(-10deg);
        }

        /* Patička */
        footer {
            text-align: center;
            margin-top: 50px;
            color: #666;
            font-size: 0.9rem;
        }

        /* Responzivní design */
        @media (max-width: 768px) {
            .programs-container {
                flex-direction: column;
                align-items: center;
            }

            .program-card {
                width: 100%;
                max-width: 350px;
            }

            h1 {
                font-size: 2rem;
            }

            .decoration {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo-container">
                <img src="favicon.jpg" alt="J.A. Komenský" class="logo floating">
            </div>
            <h1>Výukové programy pro děti</h1>
            <p class="subtitle">Zábavné učení s J.A. Komenským</p>
        </header>

        <div class="programs-container">
            <!-- Pexeso -->
            <a href="./Pexeso/index.html" class="program-card pexeso">
                <div class="card-header">
                    <h2>Pexeso</h2>
                </div>
                <div class="card-body">
                    <div class="card-icon">🎮</div>
                    <p class="card-description">
                        Trénuj vyjmenovaná slova pomocí hry pexeso! 
                        Najdi stejné dvojice kartiček a urči správné i/y.
                    </p>
                    <div class="card-button">Začít hrát</div>
                </div>
            </a>

            <!-- Podstatná jména -->
            <a href="./PodstatnaJmena/index.html" class="program-card podstatna-jmena">
                <div class="card-header">
                    <h2>Podstatná jména</h2>
                </div>
                <div class="card-body">
                    <div class="card-icon">📝</div>
                    <p class="card-description">
                        Nauč se určovat mluvnické kategorie podstatných jmen.
                        Pád, rod a číslo už pro tebe budou hračka!
                    </p>
                    <div class="card-button">Začít učení</div>
                </div>
            </a>

            <!-- Geometrie -->
            <a href="./ObvodObsah/index.html" class="program-card geometrie">
                <div class="card-header">
                    <h2>Geometrie</h2>
                </div>
                <div class="card-body">
                    <div class="card-icon">📐</div>
                    <p class="card-description">
                        Nauč se počítat obvod a obsah geometrických tvarů.
                        Interaktivní úlohy ti pomohou pochopit základy geometrie.
                    </p>
                    <div class="card-button">Začít počítat</div>
                </div>
            </a>

	<a href="./Hamster/index.html" class="program-card hamster">
    <div class="card-header">
        <h2>Určování i/y s křečkem</h2>
    </div>
    <div class="card-body">
        <div class="card-icon">🐹</div>
        <p class="card-description">
            Procvič si vyjmenovaná slova s roztomilým křečkem!
            Vyber správné i/y a zachraň křečka před pádem do toalety.
        </p>
        <div class="card-button">Začít hrát</div>
    </div>
</a>

            <!-- Pizza Fractions -->
            <a href="./Pizza/index.html" class="program-card pizza">
                <div class="card-header">
                    <h2>Pizza zlomky</h2>
                </div>
                <div class="card-body">
                    <div class="card-icon">🍕</div>
                    <p class="card-description">
                        Nauč se zlomky hravou formou! Krájej pizzu na dílky
                        a vyber správný počet kousků podle zadaného zlomku.
                    </p>
                    <div class="card-button">Začít krájení</div>
                </div>
            </a>
        </div>

        <!-- Dekorace -->
        <div class="decoration decoration-1">✏️</div>
        <div class="decoration decoration-2">📚</div>
        <div class="decoration decoration-3">🔢</div>
        <div class="decoration decoration-4">🧩</div>

        <footer>
            <p>© 2025 Výukové programy pro děti | Inspirováno odkazem J.A. Komenského</p>
        </footer>
    </div>
</body>
</html>
