import { useState, useEffect, useRef } from 'react'
import './App.css'

// Komponenta pro výběr <PERSON> 0-9 s vysou<PERSON><PERSON><PERSON> klávesnicí
function NumberSelector({ value, onChange, placeholder, disabled, inputRef, isActive, label }) {
  const [showKeyboard, setShowKeyboard] = useState(false);

  // Rozložení klávesnice 3×4 (telefonní styl)
  const keyboardLayout = [
    [1, 2, 3],
    [4, 5, 6],
    [7, 8, 9],
    [0, 'close'] // 0 a křížek pro zavření
  ];

  const handleNumberClick = (num) => {
    if (!disabled) {
      onChange(num.toString());
      setShowKeyboard(false); // Skryj klávesnici po výběru
    }
  };

  const handleCloseClick = () => {
    setShowKeyboard(false);
  };

  const toggleKeyboard = () => {
    if (!disabled) {
      setShowKeyboard(!showKeyboard);
    }
  };

  return (
    <div className={`number-selector ${isActive ? 'active' : ''}`} ref={inputRef}>
      <div className="input-group">
        <label className="input-label">{label}</label>
        <div
          className={`selected-number ${showKeyboard ? 'keyboard-open' : ''}`}
          onClick={toggleKeyboard}
        >
          {value !== '' ? value : placeholder}
        </div>
      </div>

      {showKeyboard && (
        <div className="keyboard-overlay">
          <div className="keyboard-popup">
            <div className="number-keypad">
              {keyboardLayout.map((row, rowIndex) => (
                <div key={rowIndex} className="keypad-row">
                  {row.map((key, keyIndex) => (
                    <button
                      key={keyIndex}
                      className={`keypad-btn ${
                        key === 'close' ? 'close-btn' :
                        (value == key ? 'selected' : '')
                      }`}
                      onClick={() => key === 'close' ? handleCloseClick() : handleNumberClick(key)}
                      disabled={disabled}
                    >
                      {key === 'close' ? '✕' : key}
                    </button>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function App() {
  const [dividend, setDividend] = useState(0) // dělenec
  const [divisor, setDivisor] = useState(0) // dělitel
  const [quotient, setQuotient] = useState('') // podíl
  const [remainder, setRemainder] = useState('') // zbytek
  const [score, setScore] = useState(0)
  const [attempts, setAttempts] = useState(0)
  const [feedback, setFeedback] = useState('')
  const [showAnswer, setShowAnswer] = useState(false)
  const [gameStarted, setGameStarted] = useState(false)
  const [activeSelector, setActiveSelector] = useState('quotient') // 'quotient' nebo 'remainder'

  // Refs pro automatické focusy
  const quotientRef = useRef(null)
  const remainderRef = useRef(null)

  // Generování nového příkladu (podíl vždy < 10)
  const generateNewProblem = () => {
    let newDividend, newDivisor;
    do {
      newDividend = Math.floor(Math.random() * 90) + 10; // 10-99
      newDivisor = Math.floor(Math.random() * 8) + 2; // 2-9
    } while (Math.floor(newDividend / newDivisor) >= 10); // Opakuj dokud podíl není < 10

    setDividend(newDividend);
    setDivisor(newDivisor);
    setQuotient('');
    setRemainder('');
    setFeedback('');
    setShowAnswer(false);
    setActiveSelector('quotient');

    // Automatický scroll na podíl po krátké pauze
    setTimeout(() => {
      if (quotientRef.current) {
        quotientRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  };

  // Spuštění hry
  const startGame = () => {
    setGameStarted(true);
    setScore(0);
    setAttempts(0);
    generateNewProblem();
  };

  // Kontrola odpovědi
  const checkAnswer = () => {
    const correctQuotient = Math.floor(dividend / divisor);
    const correctRemainder = dividend % divisor;
    const userQuotient = parseInt(quotient);
    const userRemainder = parseInt(remainder);

    setAttempts(attempts + 1);

    if (userQuotient === correctQuotient && userRemainder === correctRemainder) {
      setScore(score + 1); // Změněno z +10 na +1
      setFeedback('🎉 Správně! Výborně!');
      setTimeout(() => {
        generateNewProblem();
      }, 2000);
    } else {
      setFeedback(`❌ Špatně. Správná odpověď: ${correctQuotient} zbytek ${correctRemainder}`);
      setShowAnswer(true);
      setTimeout(() => {
        generateNewProblem();
      }, 3000);
    }
  };

  // Další příklad
  const nextProblem = () => {
    generateNewProblem();
  };

  // Automatický přechod na zbytek po zadání podílu
  const handleQuotientChange = (value) => {
    setQuotient(value);
    setActiveSelector('remainder');
    if (value && value.length > 0 && remainderRef.current) {
      setTimeout(() => {
        remainderRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 100);
    }
  };

  // Obsluha změny zbytku
  const handleRemainderChange = (value) => {
    setRemainder(value);
  };

  return (
    <div className="app">
      {!gameStarted ? (
        <div className="welcome-screen">
          <h1 className="title">🎯 Hra na dělení se zbytkem</h1>
          <p className="subtitle">Procvič si dělení dvouciferných čísel jednociferným číslem!</p>
          <button className="start-button" onClick={startGame}>
            🚀 Začít hrát
          </button>
        </div>
      ) : (
        <div className="game-screen">
          <div className="header">
            <div className="score-board">
              <div className="score">🏆 Skóre: {score}</div>
              <div className="attempts">📊 Pokusů: {attempts}</div>
            </div>
          </div>

          <div className="problem-container">
            <div className="problem">
              <span className="dividend">{dividend}</span>
              <span className="operator">÷</span>
              <span className="divisor">{divisor}</span>
              <span className="equals">=</span>
              <NumberSelector
                value={quotient}
                onChange={handleQuotientChange}
                placeholder="?"
                disabled={showAnswer}
                inputRef={quotientRef}
                isActive={activeSelector === 'quotient'}
                label="Podíl"
              />
              <span className="remainder-text">zbytek</span>
              <NumberSelector
                value={remainder}
                onChange={handleRemainderChange}
                placeholder="?"
                disabled={showAnswer}
                inputRef={remainderRef}
                isActive={activeSelector === 'remainder'}
                label="Zbytek"
              />
            </div>
          </div>

          <div className="buttons">
            <button
              className="check-button"
              onClick={checkAnswer}
              disabled={!quotient || !remainder || showAnswer}
            >
              ✅ Zkontrolovat
            </button>
            <button className="next-button" onClick={nextProblem}>
              ➡️ Další příklad
            </button>
          </div>

          {feedback && (
            <div className={`feedback ${feedback.includes('Správně') ? 'correct' : 'incorrect'}`}>
              {feedback}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default App
